2025年6月8日
v1.3.11 - [fix] 修复EasyRunTask任务重新开始时，结束时间没有重置的问题
        - [add] Browser支持设置代理、socks5代理

2025年4月22日
v1.3.10 - [enhance] 优化Zip压缩内存占用

2025年3月4日
v1.3.9 - [enhance] 优化maven依赖: javax.servlet和jakarta.servlet scope改成provided

2025年1月18日
v1.3.8 - [enhance] 优化Browser中对request header的处理，不区分大小写了

2025年1月15日
v1.3.7 - [enhance] Browser异步下载结果增加isException信息，以获知异步下载是否成功结束

2025年1月13日
v1.3.6 - [fix] 修复Browser工具当retry请求时，inputStream读取数据不完整的问题

2024年11月25日
v1.3.5 - [add] 重写了DateUtils对LocalDateTime和LocalDate、LocalTime的解析，覆盖ISO 8601所有格式

2024年10月18日
v1.3.4 - [add] ThreadPoolUtils增加等待所有线程结束并关闭的方法

2024年6月24日
v1.3.3 - [enhance] 优化JSON.clone对于只有getter没有setter字段的处理
       - [add] 增加压缩工具，支持制定目录进行压缩

2024年5月24日
v1.3.2 - [optimize] 优化EasyRunTask创建线程池的性能，减少重复创建线程池的次数
       - [enhance] 优化MDCUtils工具对mdc为null时的处理

2024年5月20日
v1.3.1 - [fix] 修复EasyRunTask中创建线程池没有shutdown而导致内存泄漏的问题

2024年5月12日
v1.3.0 - [add] 新增MDCUtils工具，支持将父类线程的MDC上下文带到子线程
       - [enhance] ThreadPoolUtils和EasyRunTask增加支持将父线程的MDC上下文带到子线程
       - [enhance] 减少browser工具上传大文件时的内存占用
       - [del] 移除ExecuteThem工具类，ListUtils的sum方法，Hash的sha1方法

2024年2月5日
v1.2.5 - [enhance] 修改NumberUtils.sum对mapper的校验，当提供为null时抛出异常，而不是默认指定o->o

2023年11月3日
v1.2.4 - [fix] 修复因同时支持javax.servlet-api和jakarta.servlet-api而导致方法使用时，缺少任何一个包会编译报错的问题

2023年11月2日
v1.2.3 - [enhance] 因为同时需要支持javax.servlet-api和jakarta.servlet-api，故引入两者api

2023年10月23日
v1.2.2 - [fix] 修复IOUtils.readAll(InputStream, String)方法读取文件时，最后一行没有回车符却加上回车符的问题
       - [enhance] 增加jakarta.servlet-api，同时支持jakarta.servlet-api和javax.servlet-api

2023年9月28日
v1.2.1 - [add] 新增DateUtils.diffDays(LocalDate,LocalDate)方法
       - [fix] 修复IOUtils.readAll(InputStream, String)方法读取文件时，trim掉最后一个回车符的问题
       - [add] 新增ThreadPoolUtils，代替ExecuteThem

2023年8月20日
v1.2.0 - [add] 新增algorithm包，增加DynamicBaseNumber，动态进制的数字
       - [add] ListUtils增加distinct方法，去重
       - [modify] ExecuteThem的waitAllTerminate方法不再返回数据，改为返回void，请用add方法返回的future来获取
       - [fix] 处理JSON.clone对ArrayList的SubList进行clone时报错的问题

2023年6月6日
v1.1.12 - [add] 新增ListUtils的hasDuplicateNotBlank和getDuplicatesNotBlank方法
        - [add] 新增ListUtils的of方法，等价于newList；新增replaceAll方法
        - [add] 新增ListUtils的toMapSet方法
        - [add] 新增BigDecimal2ScaleSerializer，用于BigDecimal的序列化
        - [enhance] Browser优化，当服务器端返回>=400错误时，拿getErrorStream代替getInputStream
                    请【注意】通过http返回码来判断请求是否成功，而不只依赖是否抛异常

2023年5月17日
v1.1.11 - [add] 新增DateUtils的getYear/getMonth/getDay方法，入参是LocalDate
        - [add] 新增ListUtils的getDuplicates方法，获取list中重复的元素及个数
        - [upgrade] slf4j-api升级到2.0.7;jackson到2.15.0

2023年5月5日
v1.1.10 - [enhance] 优化NumberUtils.avg方法的参数顺序，使得IDEA的提示效果更佳
        - [add] 增加3个计算百分比的方法
        - [add] DateUtils新增getFirstDayOfMonth和getLastDayOfMonth两个方法
        - [enhance] ListUtils.toMap方法返回的Map类型不再限定是LinkedHashMap，此变化将影响返回map元素的顺序，但不影响元素值
                    如有排序需求，请使用MapUtils.sortByKey/MapUtils.sortByValue方法
        - [add] 新增ListUtils.concat方法，将多个list合并成一个list

2023年3月22日
v1.1.9 - [enhance] ListUtils和SortingUtils的sortAscNullLast/sortAscNullFirst/sortDescNullLast/sortDescNullFirst要求至少传递一个比较mapper进来
       - [add] 增加SortingUtilssort(List<T> list, List<SortingField<T, ? extends Comparable<?>>> sortingFieldList)方法

2023年3月20日
v1.1.8 - [add] ListUtils增加merge方法，将多个list合并成一个list
       - [del] 删除ListUtils.sub方法，改为ListUtils.subtract
       - [enhance] 优化NumberUtils的min/max泛型，支持Comparable类型只有父类的情况

2023年1月17日
v1.1.7 - [add] ListUtils新增concatArray(Object[] ...objs)方法，将多个数组合并成一个数组
       - [add] StringTools新增nthIndexOf和nthLastIndexOf方法
       - [add] 增加LocalDateTime/LocalDate/LocalTime多种日期格式的解析支持
       - [enhance] 优化JSON解析，当传入null/空字符串时，解析为null

2022年11月21日
v1.1.6 - [fix] 修复DateUtils.format(LocalDate)抛出Unsupport field: HourOfDay的异常

2022年10月31日
v1.1.5 - [add] ListUtils增加stream相关的若干方法: toList,groupByNum,partition
       - [modify] ListUtils的groupByNum和partition限定只支持List入参，如果需要Collection，请使用Stream方式
       - [enhance] StringTools.join方法增加(String splitLetter, Collection)参数方式

2022年10月22日
v1.1.4 - [fix] 修复DateUtils.toLocalDate，当java.util.Date传入是java.sql.Date时出错的问题
       - [enhance] NumberUtils增加解析数字失败时的error log

2022年10月10日
v1.1.3 - [add] ExecuteThem支持设置等待队列的最大长度，并且当队列满时会阻塞等待
       - [enhance] 支持全为0000-00-00 00:00:00的日期的解析，解决低版本java可能解析失败的问题

2022年8月29日
v1.1.2 - [add] Browser支持设置重试时间间隔
       - [add] JSON和yaml解析支持LocalDates

2022年8月15日
v1.1.1 - [add] 增加Yaml的解析工具类YAML
       - [add] IOUtils增加读取classpath resources文件内容的方法
       - [add] ListUtils增加flat方法，打平List<List<T>>为List<T>

2022年8月7日
v1.1.0 - [add] ListUtils新增shuffle方法，随机洗乱list
       - [enhance] ListUtils.sub更名为ListUtils.subtract
       - [add] JSON增加常用的类型引用Map<String, Object>/List<Map<String, Object>
       - [add] JSON增加方法setGlobalObjectMapper
       - [deprecated] JSON.setObjectMapper
       - [enhance] JSON增加方法setThreadObjectMapper等3个方法用于支持自定义objectMapper进行json操作
       - [add] JSON增加方法parseToListMap/parseToList/toJsonFormatted

2022年6月23日
v1.0.13 - [enhance] 优化DateUtils.parse性能，将常见的格式提前，提升1~2倍性能
        - [enhance] DateUtils.parse解析开启setLenient(false)，减少解析时间的坑，例如2020-61-01解析为2025-01-01

2022年4月24日
v1.0.12 - [enhance] ListUtils多个方法接收参数由List改成Collection
        - [add] ListUtils增加isEmpty(Map)方法

2022年3月17日
v1.0.11 - [add] StringTools增加isGbkCharset方法，校验给定字符串是否全部为gbk编码
          [enhance] ZipUtils流的关闭及异常处理
          [del] ZipUtils解压inputStream
          [add] ZipUtils解压文件的操作，兼容gbk编码异常及乱码问题

2022年3月7日
v1.0.10 - [add] DateUtils增加getYear/getMonth/getDay/getHour/getMinute/getSecond方法
        - [add] NumberUtils增加divide(Integer a,Integer b,Integer scale)方法
        - [add] ListUtils增加partition方法，等同于groupByNum

2022年2月15日
v1.0.9 - [add] 增加MapUtils的transform方法

2022年1月31日
v1.0.8 - [fix] JSON的clone Date类型丢失毫秒数的问题

2022年1月25日
v1.0.7 - [add] StringTools增加计算相同前缀的长度的getSamePrefixLength方法和获得相同前缀的方法getSamePrefix
       - [add] NumberUtils增加max min对BigDecimal的比较
       - [enhance] ListUtils的sort4个方法支持多个mapper，这4个方法也同步到SortingUtils中
       - [enhance] 数字解析时自动去掉数字中的逗号

2021年12月21日
v1.0.6 - [add] ListUtils添加分组方法groupByNum

2021年11月11日
v1.0.5 - [add] ListUtils增加适用于数组的filter/forEach/transform方法
       - [add] JSON.parse支持多个泛型（原来只支持1或2个泛型）
       - [del] 移除JSON.parse的JavaType传参的方法

2021年8月26日
v1.0.4 - [add] NumberUtils增加min和max方法

2021年8月26日
v1.0.3 - [add] ListUtils增加groupBy方法，等同于toMapList方法
       - [add] NumberUtils增加percent和divide方法

2021年8月12日
v1.0.2 - [add] DateUtils增加1种日期格式

2021年7月14日
v1.0.1 - [add] JSON.parse支持TypeReference
       - [add] JSON.clone支持TypeReference
       - [fix] NumberUtils.roundUp 四舍五入及double精度的问题
       - [enhance] NumberUtils.sum，如果类型转换参数mapper不提供，默认认为item可用无需转换;
       - [add] NumberUtils.sum 无需提供类型转换参数mapper
       - [enhance] NumberUtils.avg，如果类型转换参数mapper不提供，默认认为item可用无需转换
       - [add] NumberUtils.avg 无需提供类型转换参数mapper
       - [del] NumberUtils.avg 无指定保留小数位数的方法，当计算平均值出现无限循环小数时会抛ArithmeticException

2021年7月2日
v1.0.0 - [del] 移除redis和高速缓存部分，使用该功能请用：https://github.com/pugwoo/redis-helper

2021年2月25日
v0.9.3 - [add] StringTools增加isAnyBlank/isAllBlank和isAnyEmpty/isAllEmpty方法
       - [add] StringTools增加split字符 + filter结果
       - [add] 增加NumberUtils的avg方法
       - [move] ListUtils的sum方法移动到NumberUtils中
       - [add] ListUtils增加toList(Collection<E> c)方法

2020年11月5日
v0.9.2 - [add] 增加StringTools join方法对数组的支持
       - [add] ListUtils增加isEmpty isNotEmpty方法
       - [enhance] Browser支持禁用gzip
       - [upgrade] 升级jackson 2.10到2.11.3

2020年7月30日
v0.9.1 - [enhance] redis消息队列：优化当消息堆积时的ack确认性能
       - [add] redis消息队列：增加查询消息队列状态的接口
       - [add] Browser自动支持gzip压缩

2020年7月5日
v0.9.0 - [add] DateUtils增加解析格式类型：2017-10-18T16:00:00.000+0000
       - [add] RegexUtils增加replace替换功能，支持正则表达式捕获组(captureGroup)的替换
       - [add] redis消息队列：增加nack立即响应消息消费失败，可立即被重新消费
       - [add] redis消息队列：增加removeTopic，清除整个主题消息
       - [fix] redis消息队列：复原消息时需要设置接收时间为null，以解决再次消费时误恢复消息的问题
       - [add] redis消息队列：添加sendBatch批量发送消息
       - [enhance] redis消息队列：优化中清理线程的加锁逻辑
       - [fix] redis消息队列：解决当有多个不同应用使用不同topic，但是共用一个redis实例时，topic的清理问题
       - [add] 父线程将threadLocal值传递给子线程的Callable/Runnable

2020年4月29日
v0.8.8 - [improve] 自动清理redis msgq中，处于doing列表但是实际消息已经不存在的消息
       - [modify] 日期解析中处理的时间戳，只处理2000年以后的

2020年3月31日
v0.8.7 - [improve] 对于Browser的post byte[]/InputStream默认使用Content-type=text/plain
       - [improve] 清理redis msgq中，当消息已经被ack后，还存留在doing列表的问题（不影响程序正确性）

2020年1月17日
v0.8.6 - [improve] 处理Browser工具中connection和inputStream的关闭
       - [add] 支持Browser关闭自动跳转

2019年12月19日
v0.8.5 - [improve] StringTools的join方法入参由List<Object>改成List<?>
       - [add] ExecuteThem线程池支持设定线程名称
       - [add] DateUtils支持时间戳的解析日期
       - [mod] StringTools增加isDigit方法，isEnglishLetterOrNumeric方法改名为isAlphabeticOrDigit(空字符串返回false)
       - [add] ListUtils增加array转list方法

2019年11月21日
v0.8.4 - [add] ListUtils增加交、并、差集的扩展功能：可以指定以哪个字段进行判断equals、支持多个list进行交和并集计算

2019年11月21日
v0.8.3 - [improve] Browser忽略json: HttpResponseFuture/contentBytes, 优化Browser处理cookie性能，使用线程池代替new Thread；默认去掉Referer的header，默认user-agent改成java，提供一个WIN_CHROME_AGENT
       - [add] 增加StringTools的join方法

2019年11月9日
v0.8.2 - [add] 增加LocalDate LocalTime LocalDateTime的封装
       - [improve] Browser默认POST方法不重试，默认GET方法重试1次，支持分别配置GET和POST的重试次数
       - [IMPORTANT update] 更新pom依赖：主要更新jedis到3.1.0跨大版本，支持spring boot 2.2.x版本

2019年10月26日
v0.8.1 - [add] 增加基于redis实现的带ack机制的消息队列
       - [improve] 当redis拿不到jedis连接时，抛出连接异常，代替原来的空指针异常
       - [improve] 分布式锁续期增加必须提供锁的uuid
       - [improve] JSON解析: 允许字符串值出现反斜杠\，允许自行设定JSON的ObjectMapper或修改
       - [update] 更新pom依赖

2019年10月10日
v0.8.0 - [add] 增加分布式锁@Synchronized心跳机制，支持分布式锁续期
       - [add] 增加@Synchronized和@HiSpeedCache启动日志
       - [add] 增加redisHelper的rename方法
       - [add] 增加分布式锁日志打印的开关
       - [add] 关于分布式锁可重入性的说明

2019年9月3日
v0.7.5 - [fix] 修复IOUtils.readAll(InputStream in, String charset)方法当in没有数据时抛出的异常

2019年8月29日
v0.7.4 - [add] 正则表达式增加isMatch方法
       - [del] 正则表达式去掉CaseInsensitive的几个方法
       - [add] IOUtils增加listFiles的方法

2019年8月22日
v0.7.3 - [add] RedisHelper新增key-value匹配的删除key操作，仅当key-value匹配时才删除，使用lua脚本实现
       - [improve] 限定分布式锁的解锁，使用key-value匹配的删除方法

2019年8月19日
v0.7.2 - [improve] 高速缓存的使用支持项目不配置redisHelper，因为本身默认是不开启的
       - [modify] 限定分布式锁的解锁只能由加锁者解锁，修改了RedisHelper的requireLock和releaseLock方法定义

2019年8月15日
v0.7.1 - [fix] 修复高速缓存对null值缓存的问题

2019年8月11日
v0.7.0 - [add] 正则工具加上不区分大小写的匹配
       - [improve] redis增加捕获异常的log
       - [improve] 高速缓存支持json克隆，默认关闭，需要需手动开启，最多支持2个泛型
       - [add] 高速缓存支持将数据存放到redis中，默认关闭
       - [add] 高速缓存使用多线程的方式进行更新数据，默认10个线程

2019年7月1日
v0.6.8 - [improve] Browser的HttpResponse根据Browser本身的编码进行

2019年7月1日
v0.6.7 - [improve] RedisHelper的isOk不使用ping命令来确认是否ok，采用另外方式，因为jedis 2.9.3不支持

2019年6月14日
v0.6.6 - [improve] 优化高速缓存，对于高并发情况下，也保证一个cacheKey只有一个刷新任务
       - [add] Hash增加sha1算法，标记为废弃

2019年6月6日
v0.6.5 - [add] DateUtils增加getStartTimeOfDay getEndTimeOfDay等几个工具
       - [fix] 修复高速缓存ConcurrentModificationException异常

2019年5月30日
v0.6.4 - [update] 升级pom依赖包
       - [add] Browser增加设置超时时间支持，分为连接超时和读取超时，默认为10秒和60秒

2019年5月24日
v0.6.3 - [add] 增加本地高速缓存支持
       - [del] 移除NetUtils中csrfPassed方法

2019年5月15日
v0.6.2 - [add] redisHelper增加isOk方法

2019年5月7日
v0.6.1 - [add] 引入mvel，增加动态脚本能力
       - [add] redis分布式锁支持从方法参数中提取参数作为redis锁的key

2019年4月29日
v0.6.0 - [modify] 修改pom为默认引入依赖，之前是需要手工加入，现在如果需要减少jar包依赖，则自行exclude掉

2019年4月11日
v0.5.8 - [add] 增加IOUtils从inputStream读取为byte[]的方法

2019年4月2日
v0.5.7 - [fix] 修复postJson(String httpUrl, Object toJson)方法转json错误的问题

2019年3月22日
v0.5.6 - [add] Browser增加postJson方法，支持全局设置编码字符集

2019年3月15日
v0.5.5 - [improve] 废弃redis的keys操作，使用scan代替

2019年2月20日
v0.5.4 - [fix] 修复DateUtils.getDaysToToday在每天8点钟之前的计算错误

2018年12月20日
v0.5.3 - [add] 增加DateUtils的getDaysToToday方法

2018年12月14日
v0.5.2 - [update] Browser的user agent更新
       - [modify] Browser的addRequestProperty方法修改为addRequestHeader
       - [add] 增加归并排序工具类MergeSortUtils

2018年11月12日
v0.5.1 - [improve] 工具EqualUtils增加对不同类的对象的比对

2018年11月6日
v0.5.0 - [add] 新增对比同一个类两个对象是否相等的工具EqualUtils

2018年10月3日
v0.4.9 - [add] Net.Browser提供通过HttpServletRequest设置头部方法

2018年10月1日
v0.4.8 - [fix] Net.Browser对于OutputStream下载完后应该close掉
       - [add] IOUtils增加获取一条管道的方法getPipe

2018年9月18日
v0.4.7 - [add] RedisHelper增加execute executePipeline executeTransaction 3个方法
       - [del] RedisHelper移出掉getJedisConnection方法，请使用execute

2018年9月14日
v0.4.6 - [add] ListUtils增加toMapList方法

2018年7月26日
v0.4.5 - [fix] 修复jedis拿connection时抛出异常，导致资源可能没有回收的问题

2018年6月18日
v0.4.4 - [fix] 修复NetUtils.csrfPassed对上传文件判断错误的问题

2018年6月9日
v0.4.3 - [add] ListUtils增加hasDuplicate方法

2018年6月4日
v0.4.2 - [improve] JSON转换支持将空字符设置为null赋值给对象

2018年6月3日
v0.4.1 - [add] StringTools增加isInIgnoreCase方法

2018年5月31日
v0.4.0 - [add] 新增将List数据结构转换成树形Tree数据结构的工具TreeUtils
       - [add] StringTools增加isBlank isEmpty isNotBlank isNotEmpty isIn方法
       - [improve] 更新pom.xml中slf4j-api和jackson-databind至最新版本
       - [add] 增加EnglishNumberToWords工具类

2018年5月18日
v0.3.10 -[del] 移除RedisHelperImpl的ip和port默认值，当配置不全时，不初始化

2018年5月17日
v0.3.9 - [improve] RedisHelperImpl不允许设置null值给database和port属性，且必须>=0

2018年5月6日
v0.3.8 - [add] 增加ListUtils的toSet方法

2018年4月29日
v0.3.7 - [add] 增加ListUtils的contains方法

2018年4月28日
v0.3.6 - [add] 增加ListUtils的sub方法，同时优化intersection和union方法

2018年4月27日
v0.3.5 - [add] 增加redisHelper根据pattern获得key和值的接口

2018年4月24日
v0.3.4 - [improve] 优化JSON序列化对不存在enum的支持，返回null

2018年4月19日
v0.3.3 - [improve] 优化NetUtils.csrfPassed方法，不需要再制定域名白名单了，和CORS彻底解耦

2018年4月18日
v0.3.2 - [add] 新增NetUtils.csrfPassed方法

2018年3月26日
v0.3.1 - [improve] DateUtils接口调整，parse默认不抛出异常，提供指定抛出异常的接口

2018年3月19日
v0.3.0 - [del] 删除ExcelTemplate类，woo-utils不再提供

2018年3月14日
v0.2.10- [fix] 修复JSON泛型写法的细节
v0.2.9 - [add] JSON增加泛型写法
       - [add] NumberUtils增加parseDecimal方法

2018年3月2日
v0.2.8 - [add] 增加JSON的parseArray方法
       - [add] 增加ListUtils的forEach方法

2018年2月19日
v0.2.7 - [add] 增加ListUtils的toMap方法

2018年2月12日
v0.2.6 - [add] 增加ListUtils的sum方法

2018年2月5日
v0.2.5 - [improve] 修改NumberUtils的roundUp方法为保留固定位数小数点，同时增加BigDecimal处理

2018年2月3日
v0.2.4 - [add] 增加用于redis序列化的jackson object mapper及对应的IRedisObjectConverter实现

2018年1月19日
v0.2.3 - [add] 增加NetUtils.getUrlPath方法
       - [add] 增加ListUtils的排序方法:sortAscNullLast sortAscNullFirst sortDescNullLast sortDescNullFirst

2017年12月26日
v0.2.2 - [fix] 修复Browser在异步获取数据时，碰到301/302跳转的处理问题

2017年11月30日
v0.2.1 - [add] ListUtils增加filter

2017年11月16日
v0.2.0 - [add] MapUtils.of增加更多的key/value，方便java使用类json写法构造map

2017年11月1日
v0.1.18 - [add] 增加StringTools的splitByEmptyLines、toString方法
        - [improve] DoubleUtils重命名为NumberUtils，并提供转Integer/Long不抛出异常的方法
        - [add] Browser支持设置支持任何https证书
        - [add] IOUtils增加读取整个文件为String

2017年10月14日
v0.1.17 - [add] 增加Browser的addCookie方法

2017年10月13日
v0.1.16 - [fix] redis value过滤掉null值
        - [add] 增加StringTools随机生成字符串
        - [add] 增加JSON的toMap和clone方法
        - [add] 增加DateUtils计算两个日期天数差，年份差，增加格式for年月，距离现在时间差友好形式

2017年9月21日
v0.1.15 - [add] DoubleUtils
        - [add] 增加MapUtils按值排序

2017年9月19日
v0.1.14 - [add] JSON支持当null key值时，转换成空字符串。重构DateUtils。
v0.1.13 - [del] 删除Browser中post(String httpUrl)和post(String httpUrl, OutputStream outputStream)方法，减少误用

2017年9月18日
v0.1.12 - [add] 增加MapUtils和ListUtils的方法

2017年9月14日
v0.1.11 - [fix] 修复日期格式的时区问题
v0.1.10 - [add] jackson解析支持多种日期格式，会自动适配

2017年9月13日
v0.1.9 - [add] EasyRunTask增加开始和结束时间

2017年9月11日
v0.1.8 - [add] NetUtils增加获取本地ipv4 ip列表

2017年8月28日
v0.1.7 - [add] Browser增加上传文件功能

2017年8月23日
v0.1.6 - [add] 增加zip压缩封装

2017年8月19日
v0.1.5 - [add] base64封装
       - [update] 将打cookie的默认时间修改为10年

2017年8月15日
v0.1.4 - [add] jackson封装

2017年7月29日
v0.1.3 - [improve] 升级到jdk1.8
       - [add] office-excel

2017年7月28日
v0.1.2 - [add] Hash MD5,SHA256

2017年7月21日
v0.1.1 - [add] Browser支持设置请求包头部RequestProperty

2017年7月15-19日
v0.1.0 - [add] redis支持指定链接数setMaxConnections，默认128
       - [add] redisHelper增加getAutoIncrementId方法，支持分布式自增id获取
       - [add] 增加redis以@Synchronized注解的方式控制方法串行执行
       - [improve] 优化redisLimit性能，性能提升200倍

2017年7月13日
v0.0.12 - [fix] 修复redisLimit当周期为永久时使用不了的bug

2017年7月3日
v0.0.11 - [add] Browser增加异步get/post功能

2017年5月31日
v0.0.10 - [add] 重构redis,增加若干接口,规范接口为RedisHelper
        - [del] 去除guava依赖,去除拿top domain方法
