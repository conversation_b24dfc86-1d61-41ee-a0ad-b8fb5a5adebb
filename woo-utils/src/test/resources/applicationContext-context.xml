<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	                    http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
	                    http://www.springframework.org/schema/context
	                    http://www.springframework.org/schema/context/spring-context-4.2.xsd">

	<!-- 支持注解注入 -->
	<context:component-scan base-package="com.pugwoo" />
	
	<!-- redis -->
	<bean id="redisHelper" class="com.pugwoo.wooutils.redis.impl.RedisHelperImpl">
	    <property name="host" value="*************"/>
	    <property name="port" value="6379"/>
	    <property name="password" value="devdev"/>
	    <property name="database" value="0" />
		<property name="maxConnection" value="1000" />
        <property name="redisObjectConverter" ref="jsonRedisObjectConverter"/>
	</bean>

    <bean id="jsonRedisObjectConverter" class="com.pugwoo.wooutils.redis.impl.JsonRedisObjectConverter" />

	<bean class="com.pugwoo.wooutils.redis.RedisSyncAspect" />

	<bean class="com.pugwoo.wooutils.cache.HiSpeedCacheAspect" />
	
</beans>