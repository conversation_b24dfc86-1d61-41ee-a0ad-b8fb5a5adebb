package com.pugwoo.wooutils.task;

import com.pugwoo.wooutils.log.MDCUtils;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 2015年7月21日 11:17:16
 * 简单的可以控制开始、停止、恢复或重新开始的任务控制框架。
 * <br>
 * 关于暂停：等价于 start - stop - resume
 * 重新开始：等价于 start - stop - start
 *
 * <AUTHOR>
 */
public class EasyRunTask {

	/**用于同步sync*/
	private final EasyRunTask that = this;

	/**要执行的任务实现*/
	private final ITask task;
	/**执行状态*/
	private TaskStatusEnum status = TaskStatusEnum.NEW;
	/**每次同时多线程指定的任务数，需要一批一批来，每批作为停止的单位*/
	private final int concurrentNum;

	/**抛出的异常记录[线程安全]*/
	private final List<Throwable> exceptions = new Vector<>();
	/**任务总数[线程安全]*/
	private final AtomicInteger total = new AtomicInteger(0);
	/**执行的任务总数[线程安全]*/
	private final AtomicInteger processed = new AtomicInteger(0);
	/**执行成功的任务总数[线程安全]*/
	private final AtomicInteger success = new AtomicInteger(0);
	/**执行失败的任务总数[线程安全]*/
	private final AtomicInteger fail = new AtomicInteger(0);

	/**任务开始时间*/
	private Date startTime;
	/**任务结束时间*/
	private Date endTime;

	public EasyRunTask(ITask task) {
		this.task = task;
		this.concurrentNum = 1;
	}

	public EasyRunTask(ITask task, int concurrentNum) {
		this.task = task;
		this.concurrentNum = concurrentNum;
	}

	/**
	 * 启动任务
	 * @return 操作结果
	 */
	public synchronized TaskResult start() {
		return run(true);
	}

	/**
	 * 停止之后恢复任务
	 * @return 操作结果
	 */
	public synchronized TaskResult resume() {
		return run(false);
	}

	/**
	 * 停止任务
	 * @return 操作结果
	 */
	public synchronized TaskResult stop() {
		if(status == TaskStatusEnum.RUNNING) {
			status = TaskStatusEnum.STOPPING;
			return new TaskResult(true);
		}
		return new TaskResult(false, "stop must at running status");
	}

	/**查询剩余的任务数，如果有异常，也返回0，返回0表示没有更多任务了*/
	private int getRestCount() {
		try {
			return task.getRestCount();
		} catch (Throwable e) {
			exceptions.add(e);
			return 0;
		}
	}

	private synchronized TaskResult run(boolean reset) {
		if(status == TaskStatusEnum.RUNNING || status == TaskStatusEnum.STOPPING) {
			return new TaskResult(false, "cannot start when running");
		}
		if(task == null) {
			return new TaskResult(false, "task is not assigned");
		}

		if(reset) {
			startTime = new Date();
			endTime = null;
			task.reset();
			total.set(0);
			processed.set(0);
			success.set(0);
			fail.set(0);
			exceptions.clear();
		}
		status = TaskStatusEnum.RUNNING;

		new Thread(MDCUtils.withMdc(() -> {
			ThreadPoolExecutor executeThem = concurrentNum == 1 ? null :
					ThreadPoolUtils.createThreadPool(concurrentNum, 0,
					concurrentNum, "easyRunTask", true);

			try {
				while(true) {
					synchronized (that) { // 请求停止
						if(status == TaskStatusEnum.STOPPING) {
							status = TaskStatusEnum.STOPPED;
							endTime = new Date();
							return;
						}
					}

					int restCount = getRestCount();
					if(restCount <= 0) {
						synchronized (that) { // 结束任务
							status = TaskStatusEnum.FINISHED;
							endTime = new Date();
						}
						return;
					}

					if (executeThem == null) { // 如果是1个线程，则直接执行，不用多线程，节省资源
						try {
							TaskResult result = task.runStep();
							if(result == null || !result.isSuccess()) {
								fail.incrementAndGet();
							} else {
								success.incrementAndGet();
							}
						} catch (Throwable e) {
							exceptions.add(e);
							fail.incrementAndGet();
						} finally {
							processed.incrementAndGet();
						}
					} else {
						// 多线程执行任务，实际上，是一批一批地去执行，这样才能中途控制其停下
						int nThreads = Math.min(restCount, concurrentNum);
						List<Future<String>> futures = new ArrayList<>();
						for(int i = 0; i < nThreads; i++) {
							futures.add(executeThem.submit(() -> {
								try {
									TaskResult result = task.runStep();
									if(result == null || !result.isSuccess()) {
										fail.incrementAndGet();
									} else {
										success.incrementAndGet();
									}
								} catch (Throwable e) {
									exceptions.add(e);
									fail.incrementAndGet();
								} finally {
									processed.incrementAndGet();
								}
							}, ""));
						}
						ThreadPoolUtils.waitAllFuturesDone(futures);
					}

					total.set(processed.get() + getRestCount());
				}
			} finally {
				if (executeThem != null) {
					ThreadPoolUtils.shutdownAndWaitAllTermination(executeThem); // 线程池一定要释放掉，等待所有任务完成
				}
			}
		}), "EasyRunTaskExecute").start();

		return new TaskResult(true);
	}

	/**
	 * 获得当前的任务状态
	 */
	public TaskStatusEnum getStatus() {
		return status;
	}
	/**
	 * 获得当前的异常
	 */
	public List<Throwable> getExceptions() {
		return exceptions;
	}
	/**
	 * 获得所有的记录数
	 */
	public int getTotal() {
		return total.get();
	}
	/**
	 * 获得已处理的记录数
	 */
	public int getProcessed() {
		return processed.get();
	}
	/**
	 * 获得成功的记录数
	 */
	public int getSuccess() {
		return success.get();
	}
	/**
	 * 获得失败的记录数
	 */
	public int getFail() {
		return fail.get();
	}
	/**
	 * 获得任务执行开始时间
	 */
	public Date getStartTime() {
		return startTime;
	}

	/**
	 * 获得任务执行结束时间
	 */
	public Date getEndTime() {
		return endTime;
	}

}
