package com.pugwoo.wooutils.log;

import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 该工具可以将一个runnable转换成一个将父线程MDC上下文带到运行线程的runnable
 */
public class MDCUtils {

    public static Runnable withMdc(Runnable runnable) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        return () -> {
            if (mdc != null) {
                try {
                    MDC.setContextMap(mdc);
                } catch (Throwable ignored) {
                }
            }
            runnable.run();
        };
    }

    public static <U> Supplier<U> withMdcSupplier(Supplier<U> supplier) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        return () -> {
            if (mdc != null) {
                try {
                    MDC.setContextMap(mdc);
                } catch (Throwable ignored) {
                }
            }
            return supplier.get();
        };
    }

    public static <U> Callable<U> withMdcCallable(Callable<U> callable) {
        Map<String, String> mdc = MDC.getCopyOfContextMap();
        return () -> {
            if (mdc != null) {
                try {
                    MDC.setContextMap(mdc);
                } catch (Throwable ignored) {
                }
            }
            return callable.call();
        };
    }
}
